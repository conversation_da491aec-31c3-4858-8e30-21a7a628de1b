import pandas as pd
import json
from datetime import date
from collections import Counter
import os
import argparse
import multiprocessing as mp
import gc
import time

def convert_to_json_serializable(item):
    if isinstance(item, (date, pd.Timestamp)):
        return str(item)
    elif isinstance(item, dict):
        return {key: convert_to_json_serializable(value) for key, value in item.items()}
    elif isinstance(item, list):
        return [convert_to_json_serializable(sub_item) for sub_item in item]
    return item

def process_session_batch(args):
    """批量处理多个session组的worker函数"""
    session_data_list, min_len, max_len = args
    results = []
    length_counts = Counter()

    for session_id, group_data in session_data_list:
        length = len(group_data)
        length_counts[length] += 1

        if min_len <= length <= max_len:
            # 只对需要输出的数据进行JSON序列化
            serialized = json.dumps(convert_to_json_serializable(group_data), ensure_ascii=False)
            results.append((session_id, serialized))

    return results, length_counts

def chunk_sessions(session_groups, chunk_size=100):
    """将session组分批，减少进程间通信开销"""
    chunk = []
    for item in session_groups:
        chunk.append(item)
        if len(chunk) >= chunk_size:
            yield chunk
            chunk = []
    if chunk:
        yield chunk

def get_user_operation_data_optimized(df, output_json_path, args):
    """优化版本的数据处理函数"""
    print(f"开始处理数据，总行数: {len(df)}")
    start_time = time.time()

    # 1. 高效的排序
    print("步骤1: 排序数据...")
    df_sorted = df.sort_values(['short_sessionid_', 'timestamp_']).reset_index(drop=True)

    # 2. 使用pandas的groupby，但避免apply操作
    print("步骤2: 按session分组...")
    session_groups = []

    # 使用groupby获取每个组的索引范围，避免创建大量小DataFrame
    grouped = df_sorted.groupby('short_sessionid_')

    for session_id, group_df in grouped:
        # 直接转换为records，避免中间步骤
        group_records = group_df.to_dict('records')
        # 应用JSON序列化转换
        group_records = [convert_to_json_serializable(record) for record in group_records]
        session_groups.append((session_id, group_records))

    print(f"步骤3: 分组完成，共{len(session_groups)}个session")

    # 3. 分批处理以减少内存使用和进程间通信开销
    chunk_size = 50  # 每批处理50个session
    session_chunks = list(chunk_sessions(session_groups, chunk_size))

    print(f"步骤4: 开始多进程处理，共{len(session_chunks)}个批次...")

    # 4. 使用多进程处理
    length_dist = Counter()
    ctx = mp.get_context('spawn')
    num_processes = min(mp.cpu_count(), 8)  # 减少进程数以降低开销

    with ctx.Pool(processes=num_processes) as pool:
        # 准备批次参数
        batch_args = [
            (chunk, args.trajectory_length_min, args.trajectory_length_max)
            for chunk in session_chunks
        ]

        with open(output_json_path, 'a') as fout:
            batch_count = 0
            for batch_results, batch_length_counts in pool.imap_unordered(process_session_batch, batch_args):
                batch_count += 1
                if batch_count % 10 == 0:
                    print(f"已处理 {batch_count}/{len(session_chunks)} 个批次")

                # 更新长度分布
                length_dist.update(batch_length_counts)

                # 写入结果
                for session_id, serialized in batch_results:
                    fout.write(f"{session_id}\t*#&\t{serialized}\n")

    # 清理内存
    del session_groups, session_chunks
    gc.collect()

    end_time = time.time()
    print(f"处理完成，耗时: {end_time - start_time:.2f}秒")

    # 输出长度分布
    print("Session长度分布:")
    for length in sorted(length_dist.keys()):
        count = length_dist[length]
        print(f"{length}: {count}")

def get_user_operation_data_streaming(df, output_json_path, args):
    """流式处理版本 - 适合超大数据集"""
    print(f"开始流式处理数据，总行数: {len(df)}")
    start_time = time.time()

    # 1. 排序数据
    print("步骤1: 排序数据...")
    df_sorted = df.sort_values(['short_sessionid_', 'timestamp_']).reset_index(drop=True)

    # 2. 流式处理 - 边读边写，减少内存占用
    print("步骤2: 开始流式处理...")
    length_dist = Counter()

    with open(output_json_path, 'a') as fout:
        current_session = None
        current_group = []
        processed_sessions = 0

        for _, row in df_sorted.iterrows():
            session_id = row['short_sessionid_']

            if current_session != session_id:
                # 处理上一个session组
                if current_group:
                    length = len(current_group)
                    length_dist[length] += 1

                    if args.trajectory_length_min <= length <= args.trajectory_length_max:
                        # 转换并序列化
                        group_records = [convert_to_json_serializable(record) for record in current_group]
                        serialized = json.dumps(group_records, ensure_ascii=False)
                        fout.write(f"{current_session}\t*#&\t{serialized}\n")

                    processed_sessions += 1
                    if processed_sessions % 10000 == 0:
                        print(f"已处理 {processed_sessions} 个sessions")

                # 开始新的session组
                current_session = session_id
                current_group = [row.to_dict()]
            else:
                current_group.append(row.to_dict())

        # 处理最后一个session组
        if current_group:
            length = len(current_group)
            length_dist[length] += 1

            if args.trajectory_length_min <= length <= args.trajectory_length_max:
                group_records = [convert_to_json_serializable(record) for record in current_group]
                serialized = json.dumps(group_records, ensure_ascii=False)
                fout.write(f"{current_session}\t*#&\t{serialized}\n")

            processed_sessions += 1

    end_time = time.time()
    print(f"流式处理完成，耗时: {end_time - start_time:.2f}秒")
    print(f"总共处理了 {processed_sessions} 个sessions")

    # 输出长度分布
    print("Session长度分布:")
    for length in sorted(length_dist.keys()):
        count = length_dist[length]
        print(f"{length}: {count}")

def get_user_operation_data(df, output_json_path, args):
    """智能选择处理方式的包装函数"""
    data_size = len(df)

    if data_size > 5000000:  # 超过500万行使用流式处理
        print(f"数据量较大({data_size}行)，使用流式处理模式")
        return get_user_operation_data_streaming(df, output_json_path, args)
    elif data_size > 1000000:  # 超过100万行使用优化版本
        print(f"数据量中等({data_size}行)，使用优化多进程模式")
        return get_user_operation_data_optimized(df, output_json_path, args)
    else:
        print(f"数据量较小({data_size}行)，使用流式处理模式")
        return get_user_operation_data_streaming(df, output_json_path, args)

def get_session_id(s):
    #hash=802408136&ts=1743774812155&host=&version=671103404&device=2#109385745#1743774845601
    return s.strip().split("#")[0]


def get_timestamp(s):
    return int(s.strip().split("#")[-2])


def process_pickle_file_optimized(file_path, output_json_path, args):
    """优化的单文件处理函数"""
    print(f"Processing ... {file_path}")

    # 检查文件大小
    file_size = os.path.getsize(file_path)
    print(f"文件大小: {file_size / (1024*1024*1024):.2f} GB")

    try:
        df = pd.read_pickle(file_path)
        print("数据基本信息：")
        df.info(memory_usage='deep')

        rows, columns = df.shape
        print(f"数据维度: {rows} 行 x {columns} 列")

        if rows > 0:
            print("前几行信息：")
            print(df.head())

            # 只在小数据集上显示详细信息
            if rows <= 100:
                cnt = 0
                for index, row in df.iterrows():
                    print(f"Index: {index}, appid_: {row['appid_']}, sessionid_: {row['sessionid_']}, clickitem_: {row['clickitem_']}")
                    cnt += 1
                    if cnt > 10:
                        break

            # 提取sessionid和timestamp
            print("提取session信息...")
            df['short_sessionid_'] = df['sessionid_'].apply(get_session_id)
            df['timestamp_'] = df['sessionid_'].apply(get_timestamp)

            # 处理数据
            get_user_operation_data(df, output_json_path, args)
        else:
            print("警告: 数据文件为空")

    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {e}")
        raise

def group_by_session_id(args):
    pickle_file_path = args.input_pickle_dir
    output_json_path = args.output_json_path

    target_pickles_str = args.target_pickles
    target_pickles = []
    if target_pickles_str == "all":
        target_pickles = [f for f in os.listdir(pickle_file_path) if f.endswith("pickle")]
    else:
        target_pickles = target_pickles_str.strip().split(",")

    print("target_pickles is ", target_pickles)

    # 清空输出文件
    if os.path.exists(output_json_path):
        os.remove(output_json_path)

    # pickle数据group by 之后保存成json格式
    file_list = [os.path.join(pickle_file_path, f) for f in target_pickles]

    total_start_time = time.time()
    for i, file_path in enumerate(file_list):
        print(f"\n=== 处理文件 {i+1}/{len(file_list)} ===")
        file_start_time = time.time()

        process_pickle_file_optimized(file_path, output_json_path, args)

        file_end_time = time.time()
        print(f"文件处理完成，耗时: {file_end_time - file_start_time:.2f}秒")

        # 强制垃圾回收
        gc.collect()

    total_end_time = time.time()
    print(f"\n=== 所有文件处理完成 ===")
    print(f"总耗时: {total_end_time - total_start_time:.2f}秒")


def split_txt_file(args):
    num_parts = args.split_num
    file_path = args.output_json_path
    try:
        # 读取源文件的所有行
        with open(file_path, 'r', encoding='utf-8') as file:
            lines = file.readlines()

        # 计算总行数
        total_lines = len(lines)

        # 计算每个小文件应包含的行数
        lines_per_part = total_lines // num_parts

        # 分割文件
        for i in range(num_parts):
            start_index = i * lines_per_part
            end_index = start_index + lines_per_part if i < num_parts - 1 else total_lines

            # 确定小文件的文件名
            output_file_path = os.path.join(args.split_target_dir, f'part_{i + 1}.txt')

            # 写入小文件
            with open(output_file_path, 'w', encoding='utf-8') as output_file:
                output_file.writelines(lines[start_index:end_index])

        print(f'文件已成功分割成 {num_parts} 个小文件。')
    except FileNotFoundError:
        print(f"错误：未找到文件 {file_path}。")
    except Exception as e:
        print(f"发生未知错误：{e}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="MiniP data Group by session")
    parser.add_argument("--input_pickle_dir", type=str, help="input_pickle_dir", default="")
    parser.add_argument("--target_pickles", type=str, help="all|file1,file2,file3",
                        default="all")

    parser.add_argument("--trajectory_length_min", type=int, help="trajectory_length_min",
                        default=1)
    parser.add_argument("--trajectory_length_max", type=int, help="trajectory_length_max",
                        default=20)
    parser.add_argument("--output_json_path", type=str, help="output_json_path",
                        default="")

    parser.add_argument("--split_num", type=int, help="split_num",
                        default=10)
    parser.add_argument("--split_target_dir", type=str, help="split_target_dir",
                        default="")
    parser.add_argument("--function", type=str, help="function",
                        default="group_by_session_id")


    print("Start....")
    args = parser.parse_args()
    print("args is : ")
    print(args)

    if args.function == "group_by_session_id":
        if os.path.exists(args.output_json_path):
            print(f"{args.output_json_path} has been exists, please make sure")
            exit(0)
        group_by_session_id(args)
    elif args.function == "split_txt_file":
        if os.path.exists(args.split_target_dir):
            print(f"{args.split_target_dir} has been exists, please make sure")
            exit(0)
        os.makedirs(args.split_target_dir)
        split_txt_file(args)
    else:
        print("not support function")
        exit(0)