#!/usr/bin/env python3
"""
测试优化版本的正确性
创建小规模测试数据，验证优化前后结果一致性
"""

import pandas as pd
import json
import os
import tempfile
import argparse
from datetime import datetime


def create_test_data(num_sessions=1000, avg_session_length=5):
    """创建测试数据"""
    print(f"创建测试数据: {num_sessions} 个sessions, 平均长度 {avg_session_length}")
    
    data = []
    session_id_base = "hash=123456&ts=1743774812155&host=&version=671103404&device=2"
    
    for session_idx in range(num_sessions):
        session_length = max(1, avg_session_length + (session_idx % 10) - 5)  # 变化长度
        session_id = f"{session_id_base}#{session_idx}#{1743774845601 + session_idx}"
        
        for event_idx in range(session_length):
            timestamp = 1743774845601 + session_idx * 1000 + event_idx * 100
            data.append({
                'sessionid_': f"{session_id_base}#{session_idx}#{timestamp}",
                'appid_': f"app_{session_idx % 10}",
                'clickitem_': f"item_{event_idx}",
                'timestamp': timestamp,
                'other_data': f"data_{session_idx}_{event_idx}"
            })
    
    df = pd.DataFrame(data)
    print(f"生成数据: {len(df)} 行")
    return df


def save_test_pickle(df, filename):
    """保存测试数据为pickle文件"""
    df.to_pickle(filename)
    print(f"保存测试数据到: {filename}")


def run_test_script(script_name, input_dir, pickle_file, output_file):
    """运行测试脚本"""
    import subprocess
    
    if script_name == "group_by_session_id_ultra_optimized.py":
        cmd = [
            "python", script_name,
            f"--input_pickle_dir={input_dir}",
            f"--target_pickles={pickle_file}",
            "--trajectory_length_min=1",
            "--trajectory_length_max=20",
            f"--output_json_path={output_file}"
        ]
    else:
        cmd = [
            "python", script_name,
            "--function=group_by_session_id",
            f"--input_pickle_dir={input_dir}",
            f"--target_pickles={pickle_file}",
            "--trajectory_length_min=1",
            "--trajectory_length_max=20",
            f"--output_json_path={output_file}"
        ]
    
    print(f"运行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=300  # 5分钟超时
        )
        
        if result.returncode == 0:
            print(f"✅ {script_name} 执行成功")
            return True, result.stdout
        else:
            print(f"❌ {script_name} 执行失败")
            print(f"错误: {result.stderr}")
            return False, result.stderr
            
    except subprocess.TimeoutExpired:
        print(f"❌ {script_name} 执行超时")
        return False, "Timeout"
    except Exception as e:
        print(f"❌ {script_name} 执行异常: {e}")
        return False, str(e)


def parse_output_file(filename):
    """解析输出文件"""
    if not os.path.exists(filename):
        return {}
    
    sessions = {}
    with open(filename, 'r', encoding='utf-8') as f:
        for line in f:
            parts = line.strip().split('\t*#&\t')
            if len(parts) == 2:
                session_id, json_data = parts
                try:
                    data = json.loads(json_data)
                    sessions[session_id] = data
                except json.JSONDecodeError:
                    print(f"警告: 无法解析JSON数据: {json_data[:100]}...")
    
    return sessions


def compare_results(result1, result2, name1, name2):
    """比较两个结果"""
    print(f"\n比较结果: {name1} vs {name2}")
    print("-" * 50)
    
    # 比较session数量
    print(f"{name1} sessions: {len(result1)}")
    print(f"{name2} sessions: {len(result2)}")
    
    if len(result1) != len(result2):
        print("❌ Session数量不一致")
        return False
    
    # 比较session内容
    mismatches = 0
    for session_id in result1:
        if session_id not in result2:
            print(f"❌ Session {session_id} 在 {name2} 中不存在")
            mismatches += 1
            continue
        
        data1 = result1[session_id]
        data2 = result2[session_id]
        
        if len(data1) != len(data2):
            print(f"❌ Session {session_id} 长度不一致: {len(data1)} vs {len(data2)}")
            mismatches += 1
            continue
        
        # 比较数据内容（忽略顺序）
        # 这里简化比较，只比较长度和第一个元素
        if data1 and data2:
            if set(data1[0].keys()) != set(data2[0].keys()):
                print(f"❌ Session {session_id} 字段不一致")
                mismatches += 1
    
    if mismatches == 0:
        print("✅ 结果完全一致")
        return True
    else:
        print(f"❌ 发现 {mismatches} 个不一致")
        return False


def main():
    parser = argparse.ArgumentParser(description="测试优化版本的正确性")
    parser.add_argument("--num_sessions", type=int, default=100, help="测试session数量")
    parser.add_argument("--avg_session_length", type=int, default=5, help="平均session长度")
    parser.add_argument("--keep_files", action="store_true", help="保留测试文件")
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("Group By Session 优化版本正确性测试")
    print("=" * 60)
    print(f"测试时间: {datetime.now()}")
    print(f"测试参数: {args.num_sessions} sessions, 平均长度 {args.avg_session_length}")
    print()
    
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"临时目录: {temp_dir}")
        
        # 1. 创建测试数据
        df = create_test_data(args.num_sessions, args.avg_session_length)
        
        # 2. 保存测试数据
        pickle_file = "test_data.pickle"
        pickle_path = os.path.join(temp_dir, pickle_file)
        save_test_pickle(df, pickle_path)
        
        # 3. 测试不同版本
        test_configs = [
            {
                "name": "优化版本",
                "script": "group_by_session_id.py",
                "output": os.path.join(temp_dir, "output_optimized.json")
            },
            {
                "name": "超级优化版本", 
                "script": "group_by_session_id_ultra_optimized.py",
                "output": os.path.join(temp_dir, "output_ultra.json")
            }
        ]
        
        results = {}
        
        for config in test_configs:
            if not os.path.exists(config["script"]):
                print(f"跳过 {config['name']}: 脚本不存在")
                continue
            
            print(f"\n测试 {config['name']}...")
            success, output = run_test_script(
                config["script"], 
                temp_dir, 
                pickle_file, 
                config["output"]
            )
            
            if success:
                result_data = parse_output_file(config["output"])
                results[config["name"]] = result_data
                print(f"解析结果: {len(result_data)} 个sessions")
            else:
                print(f"测试失败: {output}")
        
        # 4. 比较结果
        if len(results) >= 2:
            result_names = list(results.keys())
            compare_results(
                results[result_names[0]], 
                results[result_names[1]], 
                result_names[0], 
                result_names[1]
            )
        else:
            print("⚠️  无法进行比较，测试版本不足")
        
        # 5. 保留文件（如果需要）
        if args.keep_files:
            import shutil
            keep_dir = f"test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            shutil.copytree(temp_dir, keep_dir)
            print(f"\n📁 测试文件已保存到: {keep_dir}")
    
    print("\n✅ 正确性测试完成")


if __name__ == "__main__":
    main()
