#!/bin/bash

# Group By Session ID 性能测试脚本
# 这个脚本演示如何使用新的性能测试功能

echo "🚀 Group By Session ID 性能测试套件"
echo "=================================="

# 检查必要的依赖
echo "📋 检查依赖..."
python -c "import pandas, psutil" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "❌ 缺少必要依赖，请安装："
    echo "   pip install pandas psutil"
    exit 1
fi

echo "✅ 依赖检查通过"
echo ""

# 1. 快速测试（小数据量）
echo "🏃‍♂️ 1. 快速测试（100个sessions）"
echo "--------------------------------"
python test_optimization.py --num_sessions=100 --avg_session_length=3
echo ""

# 2. 标准测试（中等数据量）
echo "📊 2. 标准测试（1000个sessions）"
echo "--------------------------------"
python test_optimization.py --num_sessions=1000 --avg_session_length=5
echo ""

# 3. 大规模测试（如果用户确认）
read -p "🤔 是否运行大规模测试（10000个sessions，可能需要几分钟）？ [y/N]: " -n 1 -r
echo ""
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🚀 3. 大规模测试（10000个sessions）"
    echo "--------------------------------"
    python test_optimization.py --large_test
    echo ""
fi

# 4. 生成性能图表（如果matplotlib可用）
echo "📈 4. 生成性能图表测试"
echo "--------------------------------"
python -c "import matplotlib.pyplot" 2>/dev/null
if [ $? -eq 0 ]; then
    echo "✅ matplotlib可用，生成性能图表..."
    python test_optimization.py --num_sessions=500 --avg_session_length=4 --plot
else
    echo "⚠️  matplotlib未安装，跳过图表生成"
    echo "   安装命令: pip install matplotlib"
fi
echo ""

# 5. 实际数据测试（如果有pickle文件）
echo "📁 5. 实际数据测试"
echo "--------------------------------"
if ls *.pickle 1> /dev/null 2>&1; then
    echo "发现pickle文件，运行实际数据性能测试..."
    python performance_test.py --test_group_by --input_pickle_dir=./ --target_pickles=$(ls *.pickle | head -1)
else
    echo "⚠️  未发现pickle文件，跳过实际数据测试"
    echo "   如需测试实际数据，请将pickle文件放在当前目录"
fi

echo ""
echo "✅ 性能测试完成！"
echo ""
echo "📋 测试总结："
echo "   - 快速测试：验证基本功能和性能"
echo "   - 标准测试：模拟中等规模数据处理"
echo "   - 大规模测试：验证大数据处理能力"
echo "   - 图表生成：可视化性能对比"
echo "   - 实际数据：真实环境性能验证"
echo ""
echo "💡 建议："
echo "   - 如果性能提升明显，可以在生产环境使用优化版本"
echo "   - 如果内存使用过高，建议使用超级优化版本"
echo "   - 定期运行性能测试，监控优化效果"
